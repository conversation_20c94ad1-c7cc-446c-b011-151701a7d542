import '../../packages/styles/index.css'

import { BlurryBalls } from '@massimo-erp/components/complex'
import { GlobalStoreProvider } from '@massimo-erp/helpers'
import { MetaProvider } from '@solidjs/meta'
import { Router } from '@solidjs/router'
import { FileRoutes } from '@solidjs/start/router'
import { Suspense } from 'solid-js'

export default function App() {
  return (
    <Router
      root={props => (
        <MetaProvider>
          <GlobalStoreProvider>
            <BlurryBalls />
            <Suspense>{props.children}</Suspense>
          </GlobalStoreProvider>
        </MetaProvider>
      )}>
      <FileRoutes />
    </Router>
  )
}
